import React, { useState, useEffect } from "react";
import { audioStateManager } from "../services/AudioStateManager";
import { VoicesService } from "../services/VoicesService";
import { stopBackgroundMusic } from "../utils/audioUtils";
import { ControlButton } from "./ControlButton";

interface GameStartScreenProps {
  onStartGame: () => void;
  onShowRules: () => void;
  onGameEnd: (won: boolean) => void;
  aiLoading: boolean;
}

/**
 * Game Start Screen Component
 *
 * Displays the main menu with game controls and options.
 * Features:
 * - Game start button
 * - Rules button
 * - Music control button
 * - Speech/TTS control button
 * - Test buttons for development
 * - Cookie management utilities
 */
export const GameStartScreen: React.FC<GameStartScreenProps> = ({
  onStartGame,
  onShowRules,
  onGameEnd,
  aiLoading,
}) => {
  // Estados para controlar música y speech
  const [isMusicPlaying, setIsMusicPlaying] = useState(true);
  const [currentAzureTTSState, setCurrentAzureTTSState] = useState(true);
  const [audioState, setAudioState] = useState(audioStateManager.getState());

  // Instancia del servicio de voces (singleton)
  const voicesService = VoicesService.getInstance();

  /**
   * Efecto para monitorear cambios en el estado del audio
   */
  useEffect(() => {
    const updateAudioState = () => {
      const newState = audioStateManager.getState();
      setAudioState(newState);
      setIsMusicPlaying(newState.isBackgroundMusicPlaying);
    };

    // Actualizar estado inicial
    updateAudioState();

    // Polling para monitorear cambios (ya que subscribe está comentado)
    const interval = setInterval(updateAudioState, 1000);

    return () => clearInterval(interval);
  }, []);

  /**
   * Manejar click en el botón de música
   */
  const handleMusicClick = () => {
    if (audioState.isBackgroundMusicPlaying) {
      // Detener música
      stopBackgroundMusic();
      setIsMusicPlaying(false);
      console.log("🔇 Música de fondo detenida");
    } else {
      // La música se iniciará cuando el usuario acepte las cookies o inicie el juego
      setIsMusicPlaying(true);
      console.log("🎵 Música de fondo activada (se iniciará con el juego)");
    }
  };

  /**
   * Manejar click en el botón de speech/TTS
   */
  const handleSpeechClick = async () => {
    if (currentAzureTTSState) {
      // Desactivar TTS
      setCurrentAzureTTSState(false);
      console.log("🔇 Servicio de TTS desactivado");
    } else {
      // Activar TTS
      try {
        const isConfigured = await voicesService.configVoice();
        if (isConfigured) {
          setCurrentAzureTTSState(true);
          console.log("🔊 Servicio de TTS activado");
        } else {
          console.warn("⚠️ No se pudo configurar el servicio de TTS");
        }
      } catch (error) {
        console.error("❌ Error configurando TTS:", error);
      }
    }
  };
  return (
    <div className="card">
      <div className="game-container">
        <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

        {/* Welcome Back Section */}
        <div className="quick-start-section">
          <h4 className="quick-start-title">👋 ¡Bienvenido de vuelta!</h4>
          <p className="quick-start-description">
            ¿Estás listo para poner a prueba tu ingenio? Se generará un
            personaje misterioso y tendrás que adivinarlo haciendo preguntas
            inteligentes. ¡Usa tu voz para interactuar con la IA y descubre
            quién se esconde detrás del misterio!
          </p>

          <div
            className="buttons-container"
            style={{
              display: "flex",
              gap: "12px",
              flexWrap: "wrap",
              justifyContent: "center",
            }}
          >
            <button
              onClick={onStartGame}
              disabled={aiLoading}
              className="primary-button"
              style={{ flex: "1", minWidth: "200px" }}
            >
              {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
            </button>

            <button
              onClick={onShowRules}
              className="secondary-button"
              style={{
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "8px",
                padding: "12px 24px",
                fontSize: "16px",
                fontWeight: "600",
                cursor: "pointer",
                transition: "all 0.2s ease",
                flex: "0 0 auto",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#5a6268";
                e.currentTarget.style.transform = "translateY(-1px)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "#6c757d";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              📋 Ver Reglas
            </button>
          </div>

          {/* Control Buttons Section */}
          <div style={{ marginTop: "20px", textAlign: "center" }}>
            <small
              style={{ color: "#666", display: "block", marginBottom: "10px" }}
            >
              🎛️ Controles de Audio:
            </small>
            <div
              style={{
                display: "flex",
                gap: "12px",
                justifyContent: "center",
                flexWrap: "wrap",
              }}
            >
              <ControlButton
                onClick={handleMusicClick}
                type="music"
                isActive={isMusicPlaying && audioState.isBackgroundMusicPlaying}
                size="big"
              />

              <ControlButton
                type="sound"
                isActive={currentAzureTTSState}
                size="big"
                onClick={handleSpeechClick}
                className="speech-control"
              />
            </div>
          </div>

          {/* Temporary test buttons - Remove in production */}
          <div style={{ marginTop: "20px", textAlign: "center" }}>
            <small
              style={{ color: "#666", display: "block", marginBottom: "10px" }}
            >
              🧪 Botones de prueba (remover en producción):
            </small>
            <div
              style={{
                display: "flex",
                gap: "8px",
                justifyContent: "center",
                flexWrap: "wrap",
              }}
            >
              <button
                onClick={() => onGameEnd(true)}
                style={{
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                🎉 Simular Victoria
              </button>
              <button
                onClick={() => onGameEnd(false)}
                style={{
                  backgroundColor: "#dc3545",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                😔 Simular Derrota
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
